from base_plugin import BasePlugin, MethodHook, MethodReplacement
from hook_utils import find_class
from android_utils import log
from java.lang import CharSequence, String as JString
from java import jint

# Метаданные плагина
__id__ = "exteragram_advanced_text_replacer"
__name__ = "ЭКСТЕРАГРАМ Advanced Text Replacer"
__description__ = "Агрессивно заменяет ВЕСЬ текст в приложении на 'ЭКСТЕРАГРАМ', включая системные строки"
__author__ = "@exteragram_dev"
__version__ = "2.0.0"
__icon__ = "exteraPlugins/1"
__min_version__ = "11.12.0"

# Константа для замены текста
REPLACEMENT_TEXT = "ЭКСТЕРАГРАМ"

class UniversalTextHook(MethodHook):
    """Универсальный хук для замены любого текста"""
    
    def before_hooked_method(self, param):
        try:
            # Проходим по всем аргументам и заменяем текстовые
            for i, arg in enumerate(param.args):
                if isinstance(arg, (str, CharSequence)) and arg:
                    param.args[i] = REPLACEMENT_TEXT
        except Exception as e:
            log(f"UniversalTextHook error: {str(e)}")

class StringResourceHook(MethodReplacement):
    """Хук для замены строковых ресурсов"""
    
    def replace_hooked_method(self, param):
        # Всегда возвращаем наш текст вместо оригинальной строки
        return REPLACEMENT_TEXT

class LocaleControllerHook(MethodReplacement):
    """Хук для замены локализованных строк"""

    def replace_hooked_method(self, param):
        # Заменяем все локализованные строки
        return REPLACEMENT_TEXT

class StringConstructorHook(MethodHook):
    """Хук для перехвата создания строк"""

    def after_hooked_method(self, param):
        try:
            # Заменяем результат создания строки
            if param.hasResult():
                result = param.getResult()
                if result and isinstance(result, (str, CharSequence)):
                    param.setResult(REPLACEMENT_TEXT)
        except Exception as e:
            log(f"StringConstructorHook error: {str(e)}")

class ExteragramAdvancedTextPlugin(BasePlugin):
    """Продвинутый плагин для полной замены всего текста на ЭКСТЕРАГРАМ"""
    
    def on_plugin_load(self):
        """Инициализация плагина и регистрация всех возможных хуков"""
        self.log("Загрузка продвинутого плагина ЭКСТЕРАГРАМ Text Replacer...")
        
        # Основные UI элементы
        self._hook_ui_elements()
        
        # Системные строки и ресурсы
        self._hook_string_resources()
        
        # Локализация
        self._hook_localization()
        
        # Telegram-специфичные элементы
        self._hook_telegram_elements()

        # Toast и диалоги
        self._hook_toast_and_dialogs()

        # Сообщения и чаты
        self._hook_messages_and_chats()

        # Меню и контекстные меню
        self._hook_menus()

        # Уведомления
        self._hook_notifications()

        # API хуки для перехвата обновлений
        self._hook_telegram_api()

        # Дополнительные системные хуки
        self._hook_system_text()

        # Низкоуровневые хуки строк
        self._hook_string_operations()

        self.log("Продвинутый плагин ЭКСТЕРАГРАМ Text Replacer успешно загружен!")
    
    def _hook_ui_elements(self):
        """Перехват всех основных UI элементов"""
        ui_classes = [
            "android.widget.TextView",
            "android.widget.Button", 
            "android.widget.EditText",
            "android.widget.CheckBox",
            "android.widget.RadioButton",
            "android.widget.ToggleButton",
            "android.widget.Switch",
            "android.widget.TabWidget",
            "android.support.v7.widget.Toolbar",
            "androidx.appcompat.widget.Toolbar"
        ]
        
        for class_name in ui_classes:
            try:
                ui_class = find_class(class_name)
                
                # Перехватываем setText методы
                for method_name in ["setText", "setTitle", "setHint", "setContentDescription"]:
                    try:
                        # Пробуем разные сигнатуры методов
                        for param_type in [CharSequence, JString]:
                            try:
                                method = ui_class.getClass().getDeclaredMethod(method_name, param_type)
                                self.hook_method(method, UniversalTextHook())
                                self.log(f"Перехвачен {class_name}.{method_name}({param_type.__name__})")
                            except:
                                pass
                    except Exception as e:
                        continue
                        
            except Exception as e:
                self.log(f"Не удалось найти класс {class_name}: {e}")
    
    def _hook_string_resources(self):
        """Перехват системы строковых ресурсов Android"""
        try:
            # Перехватываем Resources.getString()
            ResourcesClass = find_class("android.content.res.Resources")
            
            # getString(int id)
            try:
                get_string_method = ResourcesClass.getClass().getDeclaredMethod("getString", jint)
                self.hook_method(get_string_method, StringResourceHook())
                self.log("Перехвачен Resources.getString(int)")
            except Exception as e:
                self.log(f"Не удалось перехватить Resources.getString(int): {e}")
            
            # getString(int id, Object... formatArgs)
            try:
                ObjectArrayClass = find_class("[Ljava.lang.Object;")
                get_string_formatted_method = ResourcesClass.getClass().getDeclaredMethod(
                    "getString", jint, ObjectArrayClass
                )
                self.hook_method(get_string_formatted_method, StringResourceHook())
                self.log("Перехвачен Resources.getString(int, Object...)")
            except Exception as e:
                self.log(f"Не удалось перехватить Resources.getString(int, Object...): {e}")
                
        except Exception as e:
            self.log(f"Ошибка при перехвате Resources: {e}")
    
    def _hook_localization(self):
        """Перехват системы локализации Telegram"""
        try:
            # Перехватываем LocaleController.getString()
            LocaleControllerClass = find_class("org.telegram.messenger.LocaleController")
            
            # Различные варианты getString
            for method_signature in [
                ("getString", [JString]),
                ("getString", [JString, jint]),
                ("formatString", [JString, ObjectArrayClass] if 'ObjectArrayClass' in locals() else [JString])
            ]:
                try:
                    method_name, param_types = method_signature
                    if param_types:
                        method = LocaleControllerClass.getClass().getDeclaredMethod(method_name, *param_types)
                        self.hook_method(method, LocaleControllerHook())
                        self.log(f"Перехвачен LocaleController.{method_name}")
                except Exception as e:
                    continue
                    
        except Exception as e:
            self.log(f"Ошибка при перехвате LocaleController: {e}")
    
    def _hook_telegram_elements(self):
        """Перехват Telegram-специфичных элементов"""
        telegram_classes = [
            "org.telegram.ui.ActionBar.ActionBar",
            "org.telegram.ui.ActionBar.ActionBarMenuItem", 
            "org.telegram.ui.Cells.TextCell",
            "org.telegram.ui.Cells.TextCheckCell",
            "org.telegram.ui.Cells.TextSettingsCell",
            "org.telegram.ui.Components.AlertsCreator"
        ]
        
        for class_name in telegram_classes:
            try:
                telegram_class = find_class(class_name)
                
                # Перехватываем все методы, которые могут устанавливать текст
                for method_name in ["setText", "setTitle", "setSubtitle", "setMessage", "setPositiveButton", "setNegativeButton"]:
                    try:
                        for param_type in [CharSequence, JString]:
                            try:
                                method = telegram_class.getClass().getDeclaredMethod(method_name, param_type)
                                self.hook_method(method, UniversalTextHook())
                                self.log(f"Перехвачен {class_name}.{method_name}")
                            except:
                                pass
                    except:
                        continue
                        
            except Exception as e:
                continue
    
    def _hook_toast_and_dialogs(self):
        """Перехват Toast и диалогов"""
        try:
            # Toast
            ToastClass = find_class("android.widget.Toast")
            ContextClass = find_class("android.content.Context")
            
            make_text_method = ToastClass.getClass().getDeclaredMethod(
                "makeText", ContextClass, CharSequence, jint
            )
            self.hook_method(make_text_method, UniversalTextHook())
            self.log("Перехвачен Toast.makeText")
            
            # AlertDialog
            AlertDialogClass = find_class("android.app.AlertDialog")
            AlertDialogBuilderClass = find_class("android.app.AlertDialog$Builder")
            
            for method_name in ["setTitle", "setMessage", "setPositiveButton", "setNegativeButton", "setNeutralButton"]:
                try:
                    method = AlertDialogBuilderClass.getClass().getDeclaredMethod(method_name, CharSequence)
                    self.hook_method(method, UniversalTextHook())
                    self.log(f"Перехвачен AlertDialog.Builder.{method_name}")
                except:
                    pass
                    
        except Exception as e:
            self.log(f"Ошибка при перехвате Toast/Dialog: {e}")
    
    def _hook_messages_and_chats(self):
        """Перехват текста сообщений и информации о чатах"""
        try:
            # Классы для работы с сообщениями
            message_classes = [
                "org.telegram.tgnet.TLRPC$Message",
                "org.telegram.messenger.MessageObject",
                "org.telegram.ui.Cells.ChatMessageCell",
                "org.telegram.ui.Cells.ChatActionCell",
                "org.telegram.ui.Components.ChatActivityEnterView"
            ]

            for class_name in message_classes:
                try:
                    message_class = find_class(class_name)

                    # Методы для установки текста сообщений
                    for method_name in ["setText", "setMessage", "setMessageText", "setCaption", "setDescription"]:
                        try:
                            for param_type in [CharSequence, JString]:
                                try:
                                    method = message_class.getClass().getDeclaredMethod(method_name, param_type)
                                    self.hook_method(method, UniversalTextHook())
                                    self.log(f"Перехвачен {class_name}.{method_name}")
                                except:
                                    pass
                        except:
                            continue

                except Exception as e:
                    continue

            # Перехват имен пользователей и чатов
            user_classes = [
                "org.telegram.tgnet.TLRPC$User",
                "org.telegram.tgnet.TLRPC$Chat",
                "org.telegram.ui.Cells.UserCell",
                "org.telegram.ui.Cells.ProfileSearchCell"
            ]

            for class_name in user_classes:
                try:
                    user_class = find_class(class_name)

                    for method_name in ["setName", "setTitle", "setUsername", "setStatus", "setSubtitle"]:
                        try:
                            for param_type in [CharSequence, JString]:
                                try:
                                    method = user_class.getClass().getDeclaredMethod(method_name, param_type)
                                    self.hook_method(method, UniversalTextHook())
                                    self.log(f"Перехвачен {class_name}.{method_name}")
                                except:
                                    pass
                        except:
                            continue

                except Exception as e:
                    continue

        except Exception as e:
            self.log(f"Ошибка при перехвате сообщений и чатов: {e}")

    def _hook_menus(self):
        """Перехват всех типов меню"""
        try:
            # Классы меню
            menu_classes = [
                "android.view.Menu",
                "android.view.MenuItem",
                "android.widget.PopupMenu",
                "androidx.appcompat.widget.PopupMenu",
                "org.telegram.ui.ActionBar.ActionBarPopupWindow",
                "org.telegram.ui.ActionBar.ActionBarMenuItem",
                "org.telegram.ui.Components.PopupSwipeBackLayout"
            ]

            for class_name in menu_classes:
                try:
                    menu_class = find_class(class_name)

                    # Методы для установки текста в меню
                    for method_name in ["setTitle", "setText", "setContentDescription", "add", "addSubMenu"]:
                        try:
                            for param_type in [CharSequence, JString]:
                                try:
                                    method = menu_class.getClass().getDeclaredMethod(method_name, param_type)
                                    self.hook_method(method, UniversalTextHook())
                                    self.log(f"Перехвачен {class_name}.{method_name}")
                                except:
                                    pass
                        except:
                            continue

                except Exception as e:
                    continue

        except Exception as e:
            self.log(f"Ошибка при перехвате меню: {e}")

    def _hook_notifications(self):
        """Перехват уведомлений"""
        try:
            # Классы уведомлений
            notification_classes = [
                "android.app.Notification",
                "android.app.Notification$Builder",
                "androidx.core.app.NotificationCompat",
                "androidx.core.app.NotificationCompat$Builder",
                "org.telegram.messenger.NotificationsController"
            ]

            for class_name in notification_classes:
                try:
                    notification_class = find_class(class_name)

                    # Методы для установки текста уведомлений
                    for method_name in ["setContentTitle", "setContentText", "setSubText", "setTicker", "setContentInfo"]:
                        try:
                            for param_type in [CharSequence, JString]:
                                try:
                                    method = notification_class.getClass().getDeclaredMethod(method_name, param_type)
                                    self.hook_method(method, UniversalTextHook())
                                    self.log(f"Перехвачен {class_name}.{method_name}")
                                except:
                                    pass
                        except:
                            continue

                except Exception as e:
                    continue

        except Exception as e:
            self.log(f"Ошибка при перехвате уведомлений: {e}")

    def _hook_telegram_api(self):
        """Перехват API Telegram для замены текста на уровне протокола"""
        try:
            # Регистрируем хуки для API обновлений
            self.add_hook("TL_updateNewMessage", priority=100)
            self.add_hook("TL_updateEditMessage", priority=100)
            self.add_hook("TL_updateNewChannelMessage", priority=100)
            self.add_hook("TL_updateEditChannelMessage", priority=100)
            self.add_hook("TL_updateUserName", priority=100)
            self.add_hook("TL_updateChatTitle", priority=100)

            self.log("Зарегистрированы API хуки для сообщений")

        except Exception as e:
            self.log(f"Ошибка при регистрации API хуков: {e}")

    def _hook_system_text(self):
        """Дополнительные системные хуки для полного перехвата текста"""
        try:
            # Перехват SpannableString и SpannableStringBuilder
            spannable_classes = [
                "android.text.SpannableString",
                "android.text.SpannableStringBuilder",
                "android.text.Editable"
            ]

            for class_name in spannable_classes:
                try:
                    spannable_class = find_class(class_name)

                    # Конструкторы
                    try:
                        constructor = spannable_class.getClass().getDeclaredConstructor(CharSequence)
                        self.hook_method(constructor, UniversalTextHook())
                        self.log(f"Перехвачен конструктор {class_name}")
                    except:
                        pass

                    # Методы append, insert, replace
                    for method_name in ["append", "insert", "replace"]:
                        try:
                            for param_type in [CharSequence, JString]:
                                try:
                                    method = spannable_class.getClass().getDeclaredMethod(method_name, param_type)
                                    self.hook_method(method, UniversalTextHook())
                                    self.log(f"Перехвачен {class_name}.{method_name}")
                                except:
                                    pass
                        except:
                            continue

                except Exception as e:
                    continue

            # Перехват Html.fromHtml для HTML текста
            try:
                HtmlClass = find_class("android.text.Html")
                from_html_method = HtmlClass.getClass().getDeclaredMethod("fromHtml", JString)
                self.hook_method(from_html_method, StringResourceHook())
                self.log("Перехвачен Html.fromHtml")
            except Exception as e:
                self.log(f"Не удалось перехватить Html.fromHtml: {e}")

        except Exception as e:
            self.log(f"Ошибка при перехвате системного текста: {e}")

    def _hook_string_operations(self):
        """Низкоуровневый перехват строковых операций"""
        try:
            # Перехват String.valueOf()
            StringClass = find_class("java.lang.String")

            # valueOf методы
            try:
                value_of_object = StringClass.getClass().getDeclaredMethod("valueOf", find_class("java.lang.Object"))
                self.hook_method(value_of_object, StringConstructorHook())
                self.log("Перехвачен String.valueOf(Object)")
            except Exception as e:
                self.log(f"Не удалось перехватить String.valueOf(Object): {e}")

            # Перехват StringBuilder и StringBuffer
            builder_classes = ["java.lang.StringBuilder", "java.lang.StringBuffer"]

            for class_name in builder_classes:
                try:
                    builder_class = find_class(class_name)

                    # toString метод
                    try:
                        to_string_method = builder_class.getClass().getDeclaredMethod("toString")
                        self.hook_method(to_string_method, StringConstructorHook())
                        self.log(f"Перехвачен {class_name}.toString()")
                    except:
                        pass

                    # append методы
                    try:
                        append_string = builder_class.getClass().getDeclaredMethod("append", JString)
                        self.hook_method(append_string, UniversalTextHook())
                        self.log(f"Перехвачен {class_name}.append(String)")
                    except:
                        pass

                except Exception as e:
                    continue

        except Exception as e:
            self.log(f"Ошибка при перехвате строковых операций: {e}")

    def on_update_hook(self, update_name: str, account: int, update):
        """Перехват обновлений Telegram для замены текста сообщений"""
        try:
            if update_name in ["TL_updateNewMessage", "TL_updateEditMessage",
                              "TL_updateNewChannelMessage", "TL_updateEditChannelMessage"]:

                if hasattr(update, 'message') and hasattr(update.message, 'message'):
                    if update.message.message:
                        update.message.message = REPLACEMENT_TEXT
                        self.log(f"Заменен текст сообщения в {update_name}")

                # Заменяем медиа подписи
                if hasattr(update, 'message') and hasattr(update.message, 'media'):
                    if hasattr(update.message.media, 'caption') and update.message.media.caption:
                        update.message.media.caption = REPLACEMENT_TEXT
                        self.log(f"Заменена подпись медиа в {update_name}")

            elif update_name == "TL_updateUserName":
                if hasattr(update, 'first_name'):
                    update.first_name = REPLACEMENT_TEXT
                if hasattr(update, 'last_name'):
                    update.last_name = REPLACEMENT_TEXT
                if hasattr(update, 'username'):
                    update.username = REPLACEMENT_TEXT
                self.log("Заменено имя пользователя")

            elif update_name == "TL_updateChatTitle":
                if hasattr(update, 'title'):
                    update.title = REPLACEMENT_TEXT
                self.log("Заменен заголовок чата")

        except Exception as e:
            self.log(f"Ошибка в on_update_hook: {e}")

        from base_plugin import HookResult, HookStrategy
        return HookResult(strategy=HookStrategy.MODIFY, update=update)

    def on_plugin_unload(self):
        """Выгрузка плагина"""
        self.log("Продвинутый плагин ЭКСТЕРАГРАМ Text Replacer выгружен!")
