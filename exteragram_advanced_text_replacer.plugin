from base_plugin import BasePlugin, MethodHook, MethodReplacement
from hook_utils import find_class
from android_utils import log
from java.lang import CharSequence, String as JString
from java import jint

# Метаданные плагина
__id__ = "exteragram_advanced_text_replacer"
__name__ = "ЭКСТЕРАГРАМ Advanced Text Replacer"
__description__ = "Агрессивно заменяет ВЕСЬ текст в приложении на 'ЭКСТЕРАГРАМ', включая системные строки"
__author__ = "@exteragram_dev"
__version__ = "2.0.0"
__icon__ = "exteraPlugins/1"
__min_version__ = "11.12.0"

# Константа для замены текста
REPLACEMENT_TEXT = "ЭКСТЕРАГРАМ"

class UniversalTextHook(MethodHook):
    """Универсальный хук для замены любого текста"""
    
    def before_hooked_method(self, param):
        try:
            # Проходим по всем аргументам и заменяем текстовые
            for i, arg in enumerate(param.args):
                if isinstance(arg, (str, CharSequence)) and arg:
                    param.args[i] = REPLACEMENT_TEXT
        except Exception as e:
            log(f"UniversalTextHook error: {str(e)}")

class StringResourceHook(MethodReplacement):
    """Хук для замены строковых ресурсов"""
    
    def replace_hooked_method(self, param):
        # Всегда возвращаем наш текст вместо оригинальной строки
        return REPLACEMENT_TEXT

class LocaleControllerHook(MethodReplacement):
    """Хук для замены локализованных строк"""
    
    def replace_hooked_method(self, param):
        # Заменяем все локализованные строки
        return REPLACEMENT_TEXT

class ExteragramAdvancedTextPlugin(BasePlugin):
    """Продвинутый плагин для полной замены всего текста на ЭКСТЕРАГРАМ"""
    
    def on_plugin_load(self):
        """Инициализация плагина и регистрация всех возможных хуков"""
        self.log("Загрузка продвинутого плагина ЭКСТЕРАГРАМ Text Replacer...")
        
        # Основные UI элементы
        self._hook_ui_elements()
        
        # Системные строки и ресурсы
        self._hook_string_resources()
        
        # Локализация
        self._hook_localization()
        
        # Telegram-специфичные элементы
        self._hook_telegram_elements()
        
        self.log("Продвинутый плагин ЭКСТЕРАГРАМ Text Replacer успешно загружен!")
    
    def _hook_ui_elements(self):
        """Перехват всех основных UI элементов"""
        ui_classes = [
            "android.widget.TextView",
            "android.widget.Button", 
            "android.widget.EditText",
            "android.widget.CheckBox",
            "android.widget.RadioButton",
            "android.widget.ToggleButton",
            "android.widget.Switch",
            "android.widget.TabWidget",
            "android.support.v7.widget.Toolbar",
            "androidx.appcompat.widget.Toolbar"
        ]
        
        for class_name in ui_classes:
            try:
                ui_class = find_class(class_name)
                
                # Перехватываем setText методы
                for method_name in ["setText", "setTitle", "setHint", "setContentDescription"]:
                    try:
                        # Пробуем разные сигнатуры методов
                        for param_type in [CharSequence, JString]:
                            try:
                                method = ui_class.getClass().getDeclaredMethod(method_name, param_type)
                                self.hook_method(method, UniversalTextHook())
                                self.log(f"Перехвачен {class_name}.{method_name}({param_type.__name__})")
                            except:
                                pass
                    except Exception as e:
                        continue
                        
            except Exception as e:
                self.log(f"Не удалось найти класс {class_name}: {e}")
    
    def _hook_string_resources(self):
        """Перехват системы строковых ресурсов Android"""
        try:
            # Перехватываем Resources.getString()
            ResourcesClass = find_class("android.content.res.Resources")
            
            # getString(int id)
            try:
                get_string_method = ResourcesClass.getClass().getDeclaredMethod("getString", jint)
                self.hook_method(get_string_method, StringResourceHook())
                self.log("Перехвачен Resources.getString(int)")
            except Exception as e:
                self.log(f"Не удалось перехватить Resources.getString(int): {e}")
            
            # getString(int id, Object... formatArgs)
            try:
                ObjectArrayClass = find_class("[Ljava.lang.Object;")
                get_string_formatted_method = ResourcesClass.getClass().getDeclaredMethod(
                    "getString", jint, ObjectArrayClass
                )
                self.hook_method(get_string_formatted_method, StringResourceHook())
                self.log("Перехвачен Resources.getString(int, Object...)")
            except Exception as e:
                self.log(f"Не удалось перехватить Resources.getString(int, Object...): {e}")
                
        except Exception as e:
            self.log(f"Ошибка при перехвате Resources: {e}")
    
    def _hook_localization(self):
        """Перехват системы локализации Telegram"""
        try:
            # Перехватываем LocaleController.getString()
            LocaleControllerClass = find_class("org.telegram.messenger.LocaleController")
            
            # Различные варианты getString
            for method_signature in [
                ("getString", [JString]),
                ("getString", [JString, jint]),
                ("formatString", [JString, ObjectArrayClass] if 'ObjectArrayClass' in locals() else [JString])
            ]:
                try:
                    method_name, param_types = method_signature
                    if param_types:
                        method = LocaleControllerClass.getClass().getDeclaredMethod(method_name, *param_types)
                        self.hook_method(method, LocaleControllerHook())
                        self.log(f"Перехвачен LocaleController.{method_name}")
                except Exception as e:
                    continue
                    
        except Exception as e:
            self.log(f"Ошибка при перехвате LocaleController: {e}")
    
    def _hook_telegram_elements(self):
        """Перехват Telegram-специфичных элементов"""
        telegram_classes = [
            "org.telegram.ui.ActionBar.ActionBar",
            "org.telegram.ui.ActionBar.ActionBarMenuItem", 
            "org.telegram.ui.Cells.TextCell",
            "org.telegram.ui.Cells.TextCheckCell",
            "org.telegram.ui.Cells.TextSettingsCell",
            "org.telegram.ui.Components.AlertsCreator"
        ]
        
        for class_name in telegram_classes:
            try:
                telegram_class = find_class(class_name)
                
                # Перехватываем все методы, которые могут устанавливать текст
                for method_name in ["setText", "setTitle", "setSubtitle", "setMessage", "setPositiveButton", "setNegativeButton"]:
                    try:
                        for param_type in [CharSequence, JString]:
                            try:
                                method = telegram_class.getClass().getDeclaredMethod(method_name, param_type)
                                self.hook_method(method, UniversalTextHook())
                                self.log(f"Перехвачен {class_name}.{method_name}")
                            except:
                                pass
                    except:
                        continue
                        
            except Exception as e:
                continue
    
    def _hook_toast_and_dialogs(self):
        """Перехват Toast и диалогов"""
        try:
            # Toast
            ToastClass = find_class("android.widget.Toast")
            ContextClass = find_class("android.content.Context")
            
            make_text_method = ToastClass.getClass().getDeclaredMethod(
                "makeText", ContextClass, CharSequence, jint
            )
            self.hook_method(make_text_method, UniversalTextHook())
            self.log("Перехвачен Toast.makeText")
            
            # AlertDialog
            AlertDialogClass = find_class("android.app.AlertDialog")
            AlertDialogBuilderClass = find_class("android.app.AlertDialog$Builder")
            
            for method_name in ["setTitle", "setMessage", "setPositiveButton", "setNegativeButton", "setNeutralButton"]:
                try:
                    method = AlertDialogBuilderClass.getClass().getDeclaredMethod(method_name, CharSequence)
                    self.hook_method(method, UniversalTextHook())
                    self.log(f"Перехвачен AlertDialog.Builder.{method_name}")
                except:
                    pass
                    
        except Exception as e:
            self.log(f"Ошибка при перехвате Toast/Dialog: {e}")
    
    def on_plugin_unload(self):
        """Выгрузка плагина"""
        self.log("Продвинутый плагин ЭКСТЕРАГРАМ Text Replacer выгружен!")
