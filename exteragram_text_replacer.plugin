from base_plugin import BasePlugin, MethodHook
from hook_utils import find_class
from android_utils import log
from java.lang import CharSequence, String as JString
from java import jint

# Метаданные плагина
__id__ = "exteragram_text_replacer"
__name__ = "ЭКСТЕРАГРАМ Text Replacer"
__description__ = "Заменяет весь видимый текст в приложении на 'ЭКСТЕРАГРАМ'"
__author__ = "@exteragram_dev"
__version__ = "1.0.0"
__icon__ = "exteraPlugins/1"
__min_version__ = "11.12.0"

# Константа для замены текста
REPLACEMENT_TEXT = "ЭКСТЕРАГРАМ"

class TextViewHook(MethodHook):
    """Хук для замены текста в TextView.setText()"""
    
    def before_hooked_method(self, param):
        try:
            # Заменяем первый аргумент (текст) на наш текст
            if len(param.args) > 0:
                param.args[0] = REPLACEMENT_TEXT
        except Exception as e:
            log(f"TextViewHook error: {str(e)}")

class ActionBarHook(MethodHook):
    """Хук для замены заголовков в ActionBar.setTitle()"""
    
    def before_hooked_method(self, param):
        try:
            # Заменяем аргумент заголовка
            if len(param.args) > 0:
                param.args[0] = REPLACEMENT_TEXT
        except Exception as e:
            log(f"ActionBarHook error: {str(e)}")

class ToastHook(MethodHook):
    """Хук для замены текста в Toast.makeText()"""
    
    def before_hooked_method(self, param):
        try:
            # Toast.makeText(Context context, CharSequence text, int duration)
            # Заменяем второй аргумент (текст)
            if len(param.args) > 1:
                param.args[1] = REPLACEMENT_TEXT
        except Exception as e:
            log(f"ToastHook error: {str(e)}")

class ButtonHook(MethodHook):
    """Хук для замены текста в Button.setText()"""
    
    def before_hooked_method(self, param):
        try:
            # Заменяем первый аргумент (текст)
            if len(param.args) > 0:
                param.args[0] = REPLACEMENT_TEXT
        except Exception as e:
            log(f"ButtonHook error: {str(e)}")

class ExteragramTextPlugin(BasePlugin):
    """Основной класс плагина для замены всего текста на ЭКСТЕРАГРАМ"""
    
    def on_plugin_load(self):
        """Инициализация плагина и регистрация хуков"""
        self.log("Загрузка плагина ЭКСТЕРАГРАМ Text Replacer...")
        
        # Перехватываем TextView.setText()
        self._hook_textview_methods()
        
        # Перехватываем ActionBar.setTitle()
        self._hook_actionbar_methods()
        
        # Перехватываем Toast.makeText()
        self._hook_toast_methods()
        
        # Перехватываем Button.setText()
        self._hook_button_methods()
        
        self.log("Плагин ЭКСТЕРАГРАМ Text Replacer успешно загружен!")
    
    def _hook_textview_methods(self):
        """Перехват методов TextView для замены текста"""
        try:
            TextViewClass = find_class("android.widget.TextView")
            
            # Перехватываем setText(CharSequence)
            try:
                set_text_method = TextViewClass.getClass().getDeclaredMethod("setText", CharSequence)
                self.hook_method(set_text_method, TextViewHook())
                self.log("Успешно перехвачен TextView.setText(CharSequence)")
            except Exception as e:
                self.log(f"Не удалось перехватить TextView.setText(CharSequence): {e}")
            
            # Перехватываем setText(String)
            try:
                set_text_string_method = TextViewClass.getClass().getDeclaredMethod("setText", JString)
                self.hook_method(set_text_string_method, TextViewHook())
                self.log("Успешно перехвачен TextView.setText(String)")
            except Exception as e:
                self.log(f"Не удалось перехватить TextView.setText(String): {e}")
                
        except Exception as e:
            self.log(f"Ошибка при перехвате TextView: {e}")
    
    def _hook_actionbar_methods(self):
        """Перехват методов ActionBar для замены заголовков"""
        try:
            ActionBarClass = find_class("org.telegram.ui.ActionBar.ActionBar")
            
            # Перехватываем setTitle(CharSequence)
            try:
                set_title_method = ActionBarClass.getClass().getDeclaredMethod("setTitle", CharSequence)
                self.hook_method(set_title_method, ActionBarHook())
                self.log("Успешно перехвачен ActionBar.setTitle()")
            except Exception as e:
                self.log(f"Не удалось перехватить ActionBar.setTitle(): {e}")
                
        except Exception as e:
            self.log(f"Ошибка при перехвате ActionBar: {e}")
    
    def _hook_toast_methods(self):
        """Перехват методов Toast для замены всплывающих сообщений"""
        try:
            ToastClass = find_class("android.widget.Toast")
            ContextClass = find_class("android.content.Context")
            
            # Перехватываем makeText(Context, CharSequence, int)
            try:
                make_text_method = ToastClass.getClass().getDeclaredMethod(
                    "makeText", ContextClass, CharSequence, jint
                )
                self.hook_method(make_text_method, ToastHook())
                self.log("Успешно перехвачен Toast.makeText()")
            except Exception as e:
                self.log(f"Не удалось перехватить Toast.makeText(): {e}")
                
        except Exception as e:
            self.log(f"Ошибка при перехвате Toast: {e}")
    
    def _hook_button_methods(self):
        """Перехват методов Button для замены текста кнопок"""
        try:
            ButtonClass = find_class("android.widget.Button")
            
            # Перехватываем setText(CharSequence)
            try:
                set_text_method = ButtonClass.getClass().getDeclaredMethod("setText", CharSequence)
                self.hook_method(set_text_method, ButtonHook())
                self.log("Успешно перехвачен Button.setText()")
            except Exception as e:
                self.log(f"Не удалось перехватить Button.setText(): {e}")
                
        except Exception as e:
            self.log(f"Ошибка при перехвате Button: {e}")
    
    def on_plugin_unload(self):
        """Выгрузка плагина"""
        self.log("Плагин ЭКСТЕРАГРАМ Text Replacer выгружен!")
